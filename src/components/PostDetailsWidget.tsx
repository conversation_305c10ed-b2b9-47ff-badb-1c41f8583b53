import React, { useState } from 'react';
import { ChevronDown, ChevronUp, X } from 'lucide-react';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface PostDetailsWidgetProps {
  post: RelatedPost | null;
  onClose: () => void;
}

const PostDetailsWidget: React.FC<PostDetailsWidgetProps> = ({ post, onClose }) => {
  const [expandedQAs, setExpandedQAs] = useState<string[]>([]);

  const toggleQA = (questionId: string) => {
    setExpandedQAs(prev => {
      if (prev.includes(questionId)) {
        return prev.filter(qa => qa !== questionId);
      } else {
        return [...prev, questionId];
      }
    });
  };

  if (!post) return null;

  return (
    <div className="bg-gradient-to-br from-white to-gray-50/30 border-2 border-green-500/80 rounded-2xl overflow-hidden shadow-lg transition-all duration-300 fade-in-up">
      {/* Header with Close Button */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200/60 bg-gradient-to-r from-green-50/50 to-gray-50/30">
        <h3 className="text-xl font-bold text-gray-800 pr-8 line-clamp-2">
          {post.post_title}
        </h3>
        <button
          onClick={onClose}
          className="flex-shrink-0 p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <X size={20} className="text-gray-500" />
        </button>
      </div>

      {/* Content */}
      <div>
        {/* Relevance Summary */}
        <div className="p-6 bg-gradient-to-br from-gray-50/30 via-gray-50/20 to-gray-50/30 relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-200/10 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

          <h5 className="font-semibold text-gray-700 mb-4 flex items-center gap-3 relative z-10">
            <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full shadow-sm">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
            <span className="text-base sm:text-lg">How this relates to the current post</span>
          </h5>
          <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 sm:p-5 border border-gray-100/80 shadow-sm relative z-10">
            <p className="text-gray-700 leading-relaxed text-sm sm:text-base whitespace-pre-line">
              {post.relevance_summary}
            </p>
          </div>
        </div>

        {/* Q&A Section */}
        {post.qa_pairs && post.qa_pairs.length > 0 && (
          <div className="p-6 bg-gradient-to-br from-gray-50/80 to-gray-50/40">
            <h5 className="font-bold text-gray-700 mb-6 flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                <div className="w-3 h-3 bg-white rounded-full"></div>
              </div>
              <span className="text-lg sm:text-xl">Quick Q&A ({post.qa_pairs.length})</span>
            </h5>

            <div className="space-y-3">
              {post.qa_pairs.map((qa, qaIndex) => {
                const questionId = `details-${qaIndex}`;
                const isExpanded = expandedQAs.includes(questionId);

                return (
                  <div
                    key={qaIndex}
                    className="bg-white/90 backdrop-blur-sm border border-gray-200/80 rounded-2xl overflow-hidden hover:border-green-500/60 hover:shadow-lg transition-all duration-300 individual-qa-item"
                  >
                    <button
                      onClick={() => toggleQA(questionId)}
                      className="w-full p-5 sm:p-6 text-left hover:bg-gradient-to-r hover:from-green-50/60 hover:to-gray-50/40 transition-all duration-300 group"
                    >
                      <div className="flex justify-between items-start gap-4">
                        <div className="flex items-start gap-4 flex-1">
                          <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-gray-100 to-gray-100 rounded-xl flex items-center justify-center mt-0.5 group-hover:from-green-100 group-hover:to-green-100 transition-all duration-300">
                            <div className="w-3 h-3 bg-gradient-to-br from-green-500 to-green-600 rounded-full"></div>
                          </div>
                          <span className="text-base sm:text-lg font-semibold text-gray-700 leading-relaxed group-hover:text-green-700 transition-colors duration-300">
                            {qa.question}
                          </span>
                        </div>
                        <div className="flex-shrink-0 mt-1">
                          {isExpanded ? (
                            <ChevronUp size={20} className="text-gray-500 group-hover:text-green-600 transition-colors duration-300" />
                          ) : (
                            <ChevronDown size={20} className="text-gray-500 group-hover:text-green-600 transition-colors duration-300" />
                          )}
                        </div>
                      </div>
                    </button>

                    <div
                      className={`transition-all duration-500 ease-in-out overflow-hidden ${
                        isExpanded
                          ? 'max-h-96 opacity-100'
                          : 'max-h-0 opacity-0'
                      }`}
                    >
                      <div className="px-5 sm:px-6 pb-5 sm:pb-6 border-t border-gray-200/60 bg-gradient-to-br from-green-50/30 to-gray-50/20">
                        <div className="pt-5 text-base sm:text-lg text-gray-700 leading-relaxed whitespace-pre-line pl-12 font-medium">
                          {qa.answer}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PostDetailsWidget;


import React from 'react';
import { Ch<PERSON>ronDown, ChevronUp, ExternalLink, Eye } from 'lucide-react';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface ForYouWidgetProps {
  relatedPosts: RelatedPost[];
  selectedPostIndex: number | null;
  onPostSelect: (index: number | null) => void;
}

const ForYouWidget: React.FC<ForYouWidgetProps> = ({
  relatedPosts,
  selectedPostIndex,
  onPostSelect
}) => {
  const togglePost = (index: number) => {
    if (selectedPostIndex === index) {
      // Close the details if the same post is clicked
      onPostSelect(null);
    } else {
      // Open the details for the selected post
      onPostSelect(index);
    }
  };

  const handleOpenPost = (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(url, '_blank');
  };

  return (
    <div className="relative">
      <div className="space-y-8">
        {relatedPosts.map((post, index) => (
          <div
            key={index}
            className={`bg-gradient-to-br from-white to-gray-50/30 border-2 rounded-2xl overflow-hidden shadow-md transition-all duration-300 fade-in-up ${
              selectedPostIndex === index
                ? 'border-green-500/80 shadow-lg ring-2 ring-green-500/20'
                : 'border-gray-200/80'
            }`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
          {/* Post Header - Always Visible */}
          <div className="p-4 sm:p-6">
            <div className="flex items-start gap-4 mb-4">
              <div className="flex-1">
                {/* Title with embedded link arrow - Only title is clickable */}
                <div
                  className="flex items-start gap-2 mb-3 group cursor-pointer hover:bg-gray-50/50 p-2 -m-2 rounded-lg transition-colors"
                  onClick={(e) => handleOpenPost(post.post_url, e)}
                >
                  <h4 className="font-bold text-gray-700 text-base sm:text-lg leading-tight line-clamp-2 group-hover:text-green-600 transition-colors">
                    {post.post_title}
                  </h4>
                  <ExternalLink
                    size={16}
                    className="text-gray-400 group-hover:text-green-500 transition-colors flex-shrink-0 mt-1"
                  />
                </div>

                {/* View Details Button - Left side under title */}
                <button
                  onClick={() => togglePost(index)}
                  className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors text-sm font-medium"
                >
                  <Eye size={14} />
                  <span className="hidden sm:inline">
                    {selectedPostIndex === index ? 'Hide Details' : 'Show Details'}
                  </span>
                  <span className="sm:hidden">
                    {selectedPostIndex === index ? 'Hide' : 'Show'}
                  </span>
                  {selectedPostIndex === index ? (
                    <ChevronUp size={16} />
                  ) : (
                    <ChevronDown size={16} />
                  )}
                </button>
              </div>
            </div>
          </div>


          </div>
        ))}

        {relatedPosts.length === 0 && (
          <div className="text-center py-16 px-6">
            <div className="relative mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto shadow-sm">
                <Eye size={32} className="text-gray-400" />
              </div>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-green-200/40 to-green-200/30 rounded-full"></div>
            </div>
            <h3 className="text-gray-600 text-xl font-semibold mb-2">No related posts found</h3>
            <p className="text-gray-400 text-base max-w-sm mx-auto leading-relaxed">
              We're always adding new content. Check back later for personalized recommendations!
            </p>
          </div>
        )}
      </div>


    </div>
  );
};

export default ForYouWidget;

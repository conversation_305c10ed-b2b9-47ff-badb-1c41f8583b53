
import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, ExternalLink, Eye, X } from 'lucide-react';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface ForYouWidgetProps {
  relatedPosts: RelatedPost[];
}

const ForYouWidget: React.FC<ForYouWidgetProps> = ({
  relatedPosts
}) => {
  // Create a unique key for this session based on post titles
  const sessionKey = `for-you-expanded-${relatedPosts.map(p => p.post_title).join('').slice(0, 50)}`;

  const [selectedPostIndex, setSelectedPostIndex] = useState<number | null>(null);
  const [expandedQAs, setExpandedQAs] = useState<string[]>([]);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Initialize expanded state on first load
  useEffect(() => {
    if (!hasInitialized && relatedPosts.length > 0) {
      // Try to load saved state from sessionStorage
      const savedState = sessionStorage.getItem(sessionKey);
      if (savedState) {
        try {
          const parsed = JSON.parse(savedState);
          setSelectedPostIndex(parsed.selectedPostIndex || null);
          setExpandedQAs(parsed.expandedQAs || []);
        } catch (error) {
          console.warn('Failed to parse saved state:', error);
          // Default to no post selected if no saved state
          setSelectedPostIndex(null);
        }
      } else {
        // Default to no post selected on first visit
        setSelectedPostIndex(null);
      }
      setHasInitialized(true);
    }
  }, [relatedPosts, sessionKey, hasInitialized]);

  // Save state to sessionStorage whenever it changes
  useEffect(() => {
    if (hasInitialized) {
      const stateToSave = {
        selectedPostIndex,
        expandedQAs
      };
      sessionStorage.setItem(sessionKey, JSON.stringify(stateToSave));
    }
  }, [selectedPostIndex, expandedQAs, sessionKey, hasInitialized]);

  const togglePost = (index: number) => {
    if (selectedPostIndex === index) {
      // Close the popup if the same post is clicked
      setSelectedPostIndex(null);
      // Also remove all QAs for this post when closing
      setExpandedQAs(prevQAs => prevQAs.filter(qa => !qa.startsWith(`${index}-`)));
    } else {
      // Open the popup for the selected post
      setSelectedPostIndex(index);
      // Clear QAs from previously selected post
      setExpandedQAs([]);
    }
  };

  const closeDetails = () => {
    setSelectedPostIndex(null);
    setExpandedQAs([]);
  };

  const toggleQA = (questionId: string) => {
    setExpandedQAs(prev => {
      if (prev.includes(questionId)) {
        // Remove from expanded QAs
        return prev.filter(qa => qa !== questionId);
      } else {
        // Add to expanded QAs
        return [...prev, questionId];
      }
    });
  };

  const handleOpenPost = (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(url, '_blank');
  };

  return (
    <div className="relative">
      <div className="space-y-8">
        {relatedPosts.map((post, index) => (
          <div
            key={index}
            className={`bg-gradient-to-br from-white to-gray-50/30 border-2 rounded-2xl overflow-hidden shadow-md transition-all duration-300 fade-in-up ${
              selectedPostIndex === index
                ? 'border-green-500/80 shadow-lg ring-2 ring-green-500/20'
                : 'border-gray-200/80'
            }`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
          {/* Post Header - Always Visible */}
          <div className="p-4 sm:p-6">
            <div className="flex items-start gap-4 mb-4">
              <div className="flex-1">
                {/* Title with embedded link arrow - Only title is clickable */}
                <div
                  className="flex items-start gap-2 mb-3 group cursor-pointer hover:bg-gray-50/50 p-2 -m-2 rounded-lg transition-colors"
                  onClick={(e) => handleOpenPost(post.post_url, e)}
                >
                  <h4 className="font-bold text-gray-700 text-base sm:text-lg leading-tight line-clamp-2 group-hover:text-green-600 transition-colors">
                    {post.post_title}
                  </h4>
                  <ExternalLink
                    size={16}
                    className="text-gray-400 group-hover:text-green-500 transition-colors flex-shrink-0 mt-1"
                  />
                </div>

                {/* View Details Button - Left side under title */}
                <button
                  onClick={() => togglePost(index)}
                  className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors text-sm font-medium"
                >
                  <Eye size={14} />
                  <span className="hidden sm:inline">
                    {selectedPostIndex === index ? 'Hide Details' : 'Show Details'}
                  </span>
                  <span className="sm:hidden">
                    {selectedPostIndex === index ? 'Hide' : 'Show'}
                  </span>
                  {selectedPostIndex === index ? (
                    <ChevronUp size={16} />
                  ) : (
                    <ChevronDown size={16} />
                  )}
                </button>
              </div>
            </div>
          </div>


          </div>
        ))}

        {relatedPosts.length === 0 && (
          <div className="text-center py-16 px-6">
            <div className="relative mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto shadow-sm">
                <Eye size={32} className="text-gray-400" />
              </div>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-green-200/40 to-green-200/30 rounded-full"></div>
            </div>
            <h3 className="text-gray-600 text-xl font-semibold mb-2">No related posts found</h3>
            <p className="text-gray-400 text-base max-w-sm mx-auto leading-relaxed">
              We're always adding new content. Check back later for personalized recommendations!
            </p>
          </div>
        )}
      </div>

      {/* Inline Post Details - No Dialog */}
      {selectedPostIndex !== null && (
        <div className="mt-8 bg-gradient-to-br from-white to-gray-50/30 border-2 border-green-500/80 rounded-2xl overflow-hidden shadow-lg transition-all duration-300 fade-in-up">
          {/* Header with Close Button */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200/60 bg-gradient-to-r from-green-50/50 to-gray-50/30">
            <h3 className="text-xl font-bold text-gray-800 pr-8 line-clamp-2">
              {relatedPosts[selectedPostIndex].post_title}
            </h3>
            <button
              onClick={closeDetails}
              className="flex-shrink-0 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X size={20} className="text-gray-500" />
            </button>
          </div>

          {/* Content */}
          <div>
            {/* Relevance Summary */}
            <div className="p-6 bg-gradient-to-br from-gray-50/30 via-gray-50/20 to-gray-50/30 relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-200/10 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

              <h5 className="font-semibold text-gray-700 mb-4 flex items-center gap-3 relative z-10">
                <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full shadow-sm">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <span className="text-base sm:text-lg">How this relates to the current post</span>
              </h5>
              <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 sm:p-5 border border-gray-100/80 shadow-sm relative z-10">
                <p className="text-gray-700 leading-relaxed text-sm sm:text-base whitespace-pre-line">
                  {relatedPosts[selectedPostIndex].relevance_summary}
                </p>
              </div>
            </div>

            {/* Q&A Section */}
            {relatedPosts[selectedPostIndex].qa_pairs && relatedPosts[selectedPostIndex].qa_pairs.length > 0 && (
              <div className="p-6 bg-gradient-to-br from-gray-50/80 to-gray-50/40">
                <h5 className="font-bold text-gray-700 mb-6 flex items-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                  </div>
                  <span className="text-lg sm:text-xl">Quick Q&A ({relatedPosts[selectedPostIndex].qa_pairs.length})</span>
                </h5>

                <div className="space-y-3">
                  {relatedPosts[selectedPostIndex].qa_pairs.map((qa, qaIndex) => {
                    const questionId = `${selectedPostIndex}-${qaIndex}`;
                    const isExpanded = expandedQAs.includes(questionId);

                    return (
                      <div
                        key={qaIndex}
                        className="bg-white/90 backdrop-blur-sm border border-gray-200/80 rounded-2xl overflow-hidden hover:border-green-500/60 hover:shadow-lg transition-all duration-300 individual-qa-item"
                      >
                        <button
                          onClick={() => toggleQA(questionId)}
                          className="w-full p-5 sm:p-6 text-left hover:bg-gradient-to-r hover:from-green-50/60 hover:to-gray-50/40 transition-all duration-300 group"
                        >
                          <div className="flex justify-between items-start gap-4">
                            <div className="flex items-start gap-4 flex-1">
                              <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-gray-100 to-gray-100 rounded-xl flex items-center justify-center mt-0.5 group-hover:from-green-100 group-hover:to-green-100 transition-all duration-300">
                                <div className="w-3 h-3 bg-gradient-to-br from-green-500 to-green-600 rounded-full"></div>
                              </div>
                              <span className="text-base sm:text-lg font-semibold text-gray-700 leading-relaxed group-hover:text-green-700 transition-colors duration-300">
                                {qa.question}
                              </span>
                            </div>
                            <div className="flex-shrink-0 mt-1">
                              {isExpanded ? (
                                <ChevronUp size={20} className="text-gray-500 group-hover:text-green-600 transition-colors duration-300" />
                              ) : (
                                <ChevronDown size={20} className="text-gray-500 group-hover:text-green-600 transition-colors duration-300" />
                              )}
                            </div>
                          </div>
                        </button>

                        <div
                          className={`transition-all duration-500 ease-in-out overflow-hidden ${
                            isExpanded
                              ? 'max-h-96 opacity-100'
                              : 'max-h-0 opacity-0'
                          }`}
                        >
                          <div className="px-5 sm:px-6 pb-5 sm:pb-6 border-t border-gray-200/60 bg-gradient-to-br from-green-50/30 to-gray-50/20">
                            <div className="pt-5 text-base sm:text-lg text-gray-700 leading-relaxed whitespace-pre-line pl-12 font-medium">
                              {qa.answer}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ForYouWidget;

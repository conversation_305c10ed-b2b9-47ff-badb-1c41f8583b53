
import { useState } from 'react';
import { Spark<PERSON>, User } from 'lucide-react';
import NewsLayout from '../components/NewsLayout';
import NewsWidget from '../components/NewsWidget';

const Index = () => {
  const [widgetMode, setWidgetMode] = useState<'summary' | 'related' | null>('summary');

  return (
    <div className="relative">
      <NewsLayout />
      
      {/* Control Icons - always show */}
      <div className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-50 flex flex-col gap-3 sm:gap-4">
          {/* AI Summary Button */}
          <div className="relative group">
            <button
              onClick={() => setWidgetMode(widgetMode === 'summary' ? null : 'summary')}
              className="relative p-4 sm:p-5 rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                         bg-gradient-to-br from-green-500 to-green-600 text-white
                         pulse-glow float-animation hover:shadow-[0_0_40px_rgba(34,197,94,1)]
                         active:scale-95 group-hover:rotate-12 touch-target mobile-button"
              // title="AI Summary"
            >
              <Sparkles size={24} className="sm:w-7 sm:h-7 drop-shadow-lg" />
            </button>

            {/* Tooltip */}
            <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 invisible group-hover:visible
                            transition-all duration-300 pointer-events-none hidden sm:block">
              <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap
                              shadow-lg border border-gray-700">
                AI Summary
                <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
              </div>
            </div>
          </div>

          {/* Related Posts Button */}
          <div className="relative group">
            <button
              onClick={() => setWidgetMode(widgetMode === 'related' ? null : 'related')}
              className="relative p-4 sm:p-5 rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                         bg-gradient-to-br from-gray-500 to-gray-600 text-white
                         pulse-glow float-animation hover:shadow-[0_0_40px_rgba(107,114,128,1)]
                         active:scale-95 group-hover:rotate-12 touch-target mobile-button"
              // title="For You"
              style={{ animationDelay: '1s' }}
            >
              <User size={24} className="sm:w-7 sm:h-7 drop-shadow-lg" />
            </button>

            {/* Tooltip */}
            <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 invisible group-hover:visible
                            transition-all duration-300 pointer-events-none hidden sm:block">
              <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap
                              shadow-lg border border-gray-700">
                For You
                <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
              </div>
            </div>
          </div>
        </div>
      
      <NewsWidget mode={widgetMode} onClose={() => setWidgetMode(null)} />
    </div>
  );
};

export default Index;
